import React, {
  createContext,
  ReactNode,
  useContext,
  useState,
  useEffect,
} from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import InteraktWhatsAppService from "@/services/interaktWhatsAppService";
import TestOTPService from "@/services/testOTPService";
import getServerBaseUrl from "@/envConfig";
import socketService from "@/services/socketService";
import * as ImagePicker from "expo-image-picker";
import * as FileSystem from "expo-file-system";
import * as ImageManipulator from "expo-image-manipulator";
import axios from "axios";

const BackendURL = getServerBaseUrl();

interface User {
  _id: string;
  fullName: string;
  phoneNumber: string;
  role: string;
  address?: string;
  age?: number;
  dateOfBirth?: string;
  profilePhoto?: string;
  adharCard?: string;
  panCard?: string;
  bankDetails?: string;
  status?: "pending" | "approved" | "rejected";
  isVerified?: boolean;
  dealerCode?: string;
  monthlyPoints?: number;
  yearlyPoints?: number;
}

interface RegistrationData {
  fullName: string;
  password: string;
  phoneNumber: string;
  dateOfBirth: Date;
  age: number;
  adharNumber?: string;
  panCardNumber?: string;
  pinCode: string;
  state: string;
  city: string;
  address: string;
  dealerCode: string;
  role: "Electrician" | "Distributor";
  profilePhoto?: string;
  adharCard?: string;
  panCard?: string;
  bankDetails?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (
    phoneNumber: string,
    password: string
  ) => Promise<{ success: boolean; message: string }>;
  register: (
    data: RegistrationData
  ) => Promise<{ success: boolean; message: string }>;
  sendOtpRequest: (
    phoneNumber: string
  ) => Promise<{ success: boolean; message: string }>;
  resendOtpRequest: (
    phoneNumber: string
  ) => Promise<{ success: boolean; message: string }>;
  verifyOtpAndRegister: (
    phoneNumber: string,
    otp: string,
    userData: any
  ) => Promise<{ success: boolean; message: string }>;
  forgotPasswordSendOTP: (
    phoneNumber: string
  ) => Promise<{ success: boolean; message: string }>;
  resetPasswordWithOTP: (
    phoneNumber: string,
    otp: string,
    newPassword: string
  ) => Promise<{ success: boolean; message: string }>;
  logout: () => void;
  isAuthenticated: boolean;
  addPoints: (
    points: number
  ) => Promise<{ success: boolean; message: string }>;
  processQRCode: (
    data: string
  ) => Promise<{ success: boolean; message: string }>;
  processRecharge: (
    amount: number
  ) => Promise<{ success: boolean; message: string }>;
  updateUserPoints: (
    monthlyPoints: number,
    yearlyPoints?: number
  ) => Promise<{ success: boolean; message: string }>;
  updateUserProfile: (
    profileData: any
  ) => Promise<{ success: boolean; message: string }>;
  refreshUserData: () => Promise<{
    success: boolean;
    message: string;
    data?: any;
  }>;
  verifyOtpRequest: (
    phoneNumber: string,
    otp: string
  ) => Promise<{ success: boolean; message: string }>;
  takePhoto: () => Promise<string | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [status, requestPermission] = ImagePicker.useCameraPermissions();
  const backendUrl = BackendURL;

  console.log(
    "🔧 Environment EXPO_PUBLIC_API_URL:",
    process.env.EXPO_PUBLIC_API_URL
  );
  console.log("🔧 Final backendUrl being used:", backendUrl);

  const cleanupTempFiles = async () => {
    try {
      const cacheDir = FileSystem.cacheDirectory;
      if (cacheDir) {
        const files = await FileSystem.readDirectoryAsync(cacheDir);
        for (const file of files) {
          if (file.includes('profile_') || file.includes('adhar_') || file.includes('pan_') || file.includes('bank_')) {
            await FileSystem.deleteAsync(`${cacheDir}${file}`);
            console.log(`🗑️ Deleted temporary file: ${file}`);
          }
        }
      }
    } catch (error) {
      console.error("❌ Error cleaning up temporary files:", error);
    }
  };

  const processImageForUpload = async (fileUri: string, type: 'profile' | 'adhar' | 'pan' | 'bank') => {
    try {
      // Ensure file:// prefix
      if (!fileUri.startsWith('file://')) {
        fileUri = `file://${fileUri}`;
        console.log(`📸 Added file:// prefix to URI:`, fileUri);
      }

      // Verify original file existence
      const originalFileInfo = await FileSystem.getInfoAsync(fileUri);
      console.log(`📸 Original ${type} file info:`, {
        exists: originalFileInfo.exists,
        size: originalFileInfo.size,
        uri: fileUri,
      });

      if (!originalFileInfo.exists) {
        console.error(`❌ ${type} file does not exist at:`, fileUri);
        return {
          success: false,
          message: `${type} image is no longer accessible. Please select it again.`,
        };
      }

      // Create a persistent copy
      const timestamp = Date.now();
      const persistentUri = `${FileSystem.cacheDirectory}${type}_${timestamp}.jpg`;
      await FileSystem.copyAsync({
        from: fileUri,
        to: persistentUri,
      });
      console.log(`📸 Copied ${type} image to persistent storage:`, persistentUri);

      // Verify copied file
      const copiedFileInfo = await FileSystem.getInfoAsync(persistentUri);
      console.log(`📸 Copied ${type} file info:`, {
        exists: copiedFileInfo.exists,
        size: copiedFileInfo.size,
        uri: persistentUri,
      });

      if (!copiedFileInfo.exists) {
        console.error(`❌ Copied ${type} file does not exist at:`, persistentUri);
        return {
          success: false,
          message: `Failed to process ${type} image`,
        };
      }

      // Compress the image
      const compressedImage = await ImageManipulator.manipulateAsync(
        persistentUri,
        [{ resize: { width: type === 'profile' ? 800 : 1200 } }],
        { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
      );
      console.log(`📸 Compressed ${type} image:`, {
        uri: compressedImage.uri,
        width: compressedImage.width,
        height: compressedImage.height,
      });

      // Verify compressed file
      const compressedInfo = await FileSystem.getInfoAsync(compressedImage.uri);
      console.log(`📸 Compressed ${type} file info:`, {
        exists: compressedInfo.exists,
        size: compressedInfo.size,
        uri: compressedImage.uri,
      });

      if (!compressedInfo.exists) {
        console.error(`❌ Compressed ${type} file missing`);
        return {
          success: false,
          message: `Failed to process ${type} image`,
        };
      }

      if (compressedInfo.size && compressedInfo.size > 5 * 1024 * 1024) {
        console.error(`❌ ${type} file too large:`, compressedInfo.size);
        return {
          success: false,
          message: `${type} image must be smaller than 5MB`,
        };
      }

      return {
        success: true,
        file: {
          uri: compressedImage.uri,
          type: "image/jpeg",
          name: `${type}_${timestamp}.jpg`,
        } as any,
      };
    } catch (error) {
      console.error(`❌ Error processing ${type} image:`, error);
      return {
        success: false,
        message: `Error processing ${type} image. Please try again.`,
      };
    }
  };

  const takePhoto = async () => {
    try {
      if (!status?.granted) {
        const permission = await requestPermission();
        if (!permission.granted) {
          console.error("❌ Camera permission denied");
          return null;
        }
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.7,
      });

      if (result.canceled) {
        console.log("📸 Photo capture canceled");
        return null;
      }

      let uri = result.assets[0].uri;
      console.log("📸 Photo captured:", uri);

      // Handle content:// URIs for Android
      if (uri.startsWith('content://')) {
        const timestamp = Date.now();
        const tempUri = `${FileSystem.cacheDirectory}profile_${timestamp}_temp.jpg`;
        await FileSystem.copyAsync({
          from: uri,
          to: tempUri,
        });
        uri = tempUri;
        console.log("📸 Copied content:// URI to temporary file:", uri);
      }

      // Copy to persistent storage
      const timestamp = Date.now();
      const persistentUri = `${FileSystem.cacheDirectory}profile_${timestamp}.jpg`;
      await FileSystem.copyAsync({
        from: uri,
        to: persistentUri,
      });
      console.log("📸 Copied captured photo to persistent storage:", persistentUri);

      // Verify the copied file
      const fileInfo = await FileSystem.getInfoAsync(persistentUri);
      console.log("📸 Copied photo file info:", {
        exists: fileInfo.exists,
        size: fileInfo.size,
        uri: persistentUri,
      });

      if (!fileInfo.exists) {
        console.error("❌ Copied photo does not exist at:", persistentUri);
        return null;
      }

      return persistentUri;
    } catch (error) {
      console.error("❌ Error taking photo:", error);
      return null;
    }
  };

  useEffect(() => {
    checkStoredAuth();
    // Periodic cleanup of temporary files
    const cleanupInterval = setInterval(cleanupTempFiles, 1000 * 60 * 60); // Run every hour
    return () => clearInterval(cleanupInterval);
  }, []);

  useEffect(() => {
    if (user) {
      console.log("🔌 User logged in, connecting to Socket.IO...");
      socketService.connect();
      const unsubscribePointsUpdate = socketService.onPointsUpdate((data) => {
        console.log("📊 Received real-time points update:", data);
        setUser((prevUser) => {
          if (prevUser) {
            const updatedUser = {
              ...prevUser,
              monthlyPoints: data.monthlyPoints,
              yearlyPoints: data.yearlyPoints,
            };
            AsyncStorage.setItem("user", JSON.stringify(updatedUser)).catch(
              (error) => {
                console.error("❌ Error updating stored user data:", error);
              }
            );
            return updatedUser;
          }
          return prevUser;
        });
      });
      const unsubscribeConnection = socketService.onConnectionChange(
        (connected) => {
          console.log(
            `🔌 Socket connection status: ${
              connected ? "Connected" : "Disconnected"
            }`
          );
        }
      );
      socketService.reAuthenticate();
      return () => {
        console.log("🔌 Cleaning up socket listeners...");
        unsubscribePointsUpdate();
        unsubscribeConnection();
      };
    } else {
      console.log("🔌 User logged out, disconnecting from Socket.IO...");
      socketService.disconnect();
    }
  }, [user?._id]);

  const checkStoredAuth = async () => {
    try {
      const storedToken = await AsyncStorage.getItem("authToken");
      const storedUser = await AsyncStorage.getItem("user");
      if (storedToken && storedUser) {
        const parsedStoredUser = JSON.parse(storedUser);
        setUser(parsedStoredUser);
        try {
          console.log("🔄 Verifying token and fetching fresh user data...");
          const response = await fetch(`${backendUrl}api/auth/verify-token`, {
            method: "GET",
            headers: {
              Authorization: `Bearer ${storedToken}`,
              "Content-Type": "application/json",
            },
          });
          if (response.ok) {
            const data = await response.json();
            console.log(
              "✅ Token verification successful, received user data:",
              data
            );
            if (data.success && data.user) {
              setUser(data.user);
              await AsyncStorage.setItem("user", JSON.stringify(data.user));
              console.log("✅ User data updated with fresh points:", {
                monthlyPoints: data.user.monthlyPoints,
                yearlyPoints: data.user.yearlyPoints,
              });
            } else {
              console.log("❌ Token verification failed, clearing storage");
              await AsyncStorage.removeItem("authToken");
              await AsyncStorage.removeItem("user");
              setUser(null);
            }
          } else {
            console.log(
              "❌ Token verification request failed, clearing storage"
            );
            await AsyncStorage.removeItem("authToken");
            await AsyncStorage.removeItem("user");
            setUser(null);
          }
        } catch (error) {
          console.log(
            "⚠️ Token verification failed due to network error, using stored user data:",
            error
          );
        }
      }
    } catch (error) {
      console.error("❌ Error checking stored auth:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const testNetworkConnectivity = async () => {
    try {
      console.log("🔧 Testing network connectivity to:", backendUrl);
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      const response = await fetch(`${backendUrl}/`, {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
        signal: controller.signal,
      });
      clearTimeout(timeoutId);
      console.log("🔧 Network test response status:", response.status);
      console.log("🔧 Network test response ok:", response.ok);
      return response.ok;
    } catch (error: any) {
      console.log("🔧 Network test failed:", error.message);
      console.log(
        "ℹ️ This is normal if the backend server is not running locally"
      );
      console.log("ℹ️ The app will still function with fallback services");
      return false;
    }
  };

  React.useEffect(() => {
    testNetworkConnectivity();
  }, []);

  const login = async (phoneNumber: string, password: string) => {
    setIsLoading(true);
    console.log("🔧 Testing connectivity before login...");
    const isConnected = await testNetworkConnectivity();
    if (!isConnected) {
      console.log(
        "🚨 Network connectivity test failed, but proceeding with login attempt..."
      );
    }
    const loginUrl = `${backendUrl}api/auth/login`;
    try {
      console.log("🔄 [UPDATED] Attempting login to:", loginUrl);
      console.log("🔄 [UPDATED] Login payload:", {
        phoneNumber,
        password: "***",
      });
      console.log("🔄 [UPDATED] Backend URL:", backendUrl);
      console.log("🔄 [UPDATED] Full URL being used:", loginUrl);
      const res = await fetch(loginUrl, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ phoneNumber, password }),
      });
      console.log("Response status:", res.status);
      console.log("Response headers:", res.headers);
      const contentType = res.headers.get("content-type");
      if (!contentType || !contentType.includes("application/json")) {
        console.error("🚨 Server returned non-JSON response:", contentType);
        const text = await res.text();
        console.error("🚨 Response text:", text.substring(0, 200) + "...");
        return {
          success: false,
          message: "Server returned invalid response format",
        };
      }
      const data = await res.json();
      console.log("Response data:", data);
      if (res.ok && data.success) {
        const userData = data.data.user;
        const token = data.data.token;
        await AsyncStorage.setItem("authToken", token);
        await AsyncStorage.setItem("user", JSON.stringify(userData));
        setUser(userData);
        console.log("Logged-in user:", userData);
        setTimeout(() => {
          socketService.reAuthenticate();
        }, 1000);
        return { success: true, message: "Login successful" };
      } else {
        console.error("Login failed:", data);
        return { success: false, message: data.message || "Login failed" };
      }
    } catch (error: any) {
      console.error("🚨 Login error:", error);
      console.error("🚨 Login error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
      if (
        error.message.includes("Network request failed") ||
        error.message.includes("fetch") ||
        error.message.includes("JSON Parse error")
      ) {
        console.error(
          "❌ Network error during login - backend server may be unreachable"
        );
        return {
          success: false,
          message:
            "Cannot connect to server. Please check your internet connection and try again.",
        };
      }
      return { success: false, message: `Login failed: ${error.message}` };
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegistrationData) => {
    setIsLoading(true);
    try {
      console.log("📝 frontend Register function called:", {
        ...data,
        password: "[HIDDEN]",
        dateOfBirth: data.dateOfBirth ? data.dateOfBirth.toString() : "NULL",
      });

      const formData = new FormData();
      formData.append("fullName", data.fullName);
      formData.append("password", data.password);
      formData.append("phoneNumber", data.phoneNumber);
      const dateOfBirth =
        data.dateOfBirth instanceof Date
          ? data.dateOfBirth
          : new Date(data.dateOfBirth);
      console.log("📅 Processing dateOfBirth:", dateOfBirth);
      formData.append("dateOfBirth", dateOfBirth.toISOString().split("T")[0]);
      formData.append("age", data.age.toString());
      formData.append("pinCode", data.pinCode);
      formData.append("state", data.state);
      formData.append("city", data.city);
      formData.append("address", data.address);
      formData.append("dealerCode", data.dealerCode);
      formData.append("role", data.role);
      if (data.adharNumber) formData.append("adharNumber", data.adharNumber);
      if (data.panCardNumber)
        formData.append("panCardNumber", data.panCardNumber);

      // Process profile photo
      if (data.profilePhoto) {
        formData.append("profilePhoto", data.profilePhoto);
      }

      // Process adhar card
      if (data.adharCard) {
        formData.append("adharCard", data.adharCard);
      }

      // Process pan card
      if (data.panCard) {
        formData.append("panCard", data.panCard);
      }

      // Process bank details
      if (data.bankDetails) {
        const fileInfo = await FileSystem.getInfoAsync(data.bankDetails);
        console.log("📸 Bank details pre-processing check:", {
          exists: fileInfo.exists,
          uri: data.bankDetails,
        });
        if (!fileInfo.exists) {
          console.error("❌ Bank details does not exist before processing");
          return {
            success: false,
            message: "Bank details image is no longer accessible. Please select it again.",
          };
        }
        const processedBank = await processImageForUpload(data.bankDetails, "bank");
        if (!processedBank.success) return processedBank;
        formData.append("bankDetails", processedBank.file);
      }

      const registerUrl = `${backendUrl}api/auth/register`;
      console.log("🌐 Registration URL:", registerUrl);
      console.log("📤 Sending registration request...");

      let attempt = 1;
      const maxRetries = 3;
      while (attempt <= maxRetries) {
        try {
          console.log(`📤 Attempt ${attempt} of ${maxRetries}`);
          const res = await axios.post(registerUrl, formData, {
            headers: { "Content-Type": "multipart/form-data" },
            timeout: 20000,
          });
          console.log("📡 Axios response:", res.status, res.data);

          return {
            success: res.status === 200 || res.status === 201,
            message: res.data.message || "Registration successful",
          };
        } catch (error: any) {
          console.error(`🚨 Attempt ${attempt} failed:`, error.message, error.response?.data);
          if (attempt === maxRetries) {
            return {
              success: false,
              message: `Registration failed: ${error.message}`,
            };
          }
          attempt++;
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
    } catch (error: any) {
      console.error("🚨 Registration error:", error);
      console.error("🚨 Registration error details:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });
      return {
        success: false,
        message: error.response?.data?.message || "Registration failed. Please try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const sendOtpRequest = async (phoneNumber: string) => {
    try {
      setIsLoading(true);
      console.log("🚀 AUTHCONTEXT: Sending OTP to:", phoneNumber);
      console.log("📱 Using Interakt WhatsApp service for registration...");
      const whatsappResult = await InteraktWhatsAppService.sendOTP(phoneNumber);
      if (whatsappResult.success) {
        console.log("✅ Registration OTP sent successfully via WhatsApp");
        return {
          success: true,
          message:
            "OTP sent successfully to your WhatsApp. Please check your messages.",
        };
      } else {
        console.error("❌ WhatsApp service failed:", whatsappResult.message);
        console.log("🔄 WhatsApp failed, trying test service as fallback...");
        const testResult = await TestOTPService.sendOTP(phoneNumber);
        if (testResult.success) {
          console.log("✅ Test OTP service succeeded as fallback");
          return {
            success: true,
            message:
              "OTP sent successfully via test service. Check console for OTP.",
          };
        } else {
          console.error("❌ Both services failed");
          return {
            success: false,
            message: "Failed to send OTP. Please try again later.",
          };
        }
      }
    } catch (error) {
      console.error("❌ AUTHCONTEXT: Send OTP error:", error);
      return {
        success: false,
        message: "Network error while sending OTP",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtpAndRegister = async (
    phoneNumber: string,
    otp: string,
    userData: any
  ) => {
    try {
      setIsLoading(true);
      console.log(
        "🔍 AUTHCONTEXT: Verifying OTP for registration:",
        phoneNumber
      );
      console.log("🔍 Using Interakt WhatsApp service for verification...");
      let verificationResult = await InteraktWhatsAppService.verifyOTP(
        phoneNumber,
        otp
      );
      if (!verificationResult.success) {
        console.log(
          "🔄 Interakt verification failed, trying test service as fallback..."
        );
        verificationResult = await TestOTPService.verifyOTP(phoneNumber, otp);
      }
      if (verificationResult.success) {
        console.log(
          "✅ OTP verified successfully, proceeding with registration"
        );
        const registrationResult = await register(userData);
        console.log("REGISTRATION_RESULT:", registrationResult);
        if (registrationResult.success) {
          console.log("✅ Registration successful");
          return {
            success: true,
            message:
              "Registration completed successfully! Please login with your credentials.",
          };
        } else {
          console.error("❌ Registration failed:", registrationResult.message);
          return {
            success: false,
            message:
              registrationResult.message ||
              "Registration failed. Please try again.",
          };
        }
      } else {
        console.error(
          "❌ OTP verification failed:",
          verificationResult.message
        );
        return {
          success: false,
          message:
            verificationResult.message || "Invalid OTP. Please try again.",
        };
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      return {
        success: false,
        message: "Verification failed. Please try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const resendOtpRequest = async (phoneNumber: string) => {
    try {
      setIsLoading(true);
      console.log("🔄 AUTHCONTEXT: Resending OTP to:", phoneNumber);
      console.log("🔄 Using Interakt WhatsApp service for resend...");
      const whatsappResult = await InteraktWhatsAppService.resendOTP(
        phoneNumber
      );
      if (whatsappResult.success) {
        console.log("✅ OTP resent successfully via WhatsApp");
        return {
          success: true,
          message: "OTP resent successfully to your WhatsApp.",
        };
      } else {
        console.log(
          "🔄 WhatsApp resend failed, trying test service as fallback..."
        );
        const testResult = await TestOTPService.resendOTP(phoneNumber);
        if (testResult.success) {
          console.log("✅ Test OTP service succeeded as fallback");
          return {
            success: true,
            message: "OTP resent successfully. Check console for OTP.",
          };
        } else {
          console.error("❌ Both resend services failed");
          return {
            success: false,
            message: "Failed to resend OTP. Please try again.",
          };
        }
      }
    } catch (error) {
      console.error("OTP resend error:", error);
      return {
        success: false,
        message: "Network error. Please check your connection and try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await AsyncStorage.removeItem("authToken");
      await AsyncStorage.removeItem("user");
      await cleanupTempFiles();
    } catch (error) {
      console.error("Error clearing auth data:", error);
    }
    socketService.disconnect();
    setUser(null);
  };

  const addPoints = async (points: number) => {
    console.log("Add points function called with:", points);
    return {
      success: true,
      message: `Added ${points} points successfully!`,
    };
  };

  const processQRCode = async (data: string) => {
    try {
      const storedToken = await AsyncStorage.getItem("authToken");
      if (!storedToken) {
        return {
          success: false,
          message: "Authentication required. Please login again.",
        };
      }
      const response = await fetch(`${backendUrl}api/qr/process`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedToken}`,
        },
        body: JSON.stringify({ qrData: data }),
      });
      const result = await response.json();
      if (result.success) {
        setUser((prev) =>
          prev
            ? {
                ...prev,
                monthlyPoints: result.totalMonthlyPoints,
                yearlyPoints: result.totalYearlyPoints,
              }
            : null
        );
      }
      return result;
    } catch (error) {
      console.error("QR processing error:", error);
      return {
        success: false,
        message: "Failed to process QR code",
      };
    }
  };

  const processRecharge = async (amount: number) => {
    console.log("Process recharge function called with:", amount);
    return {
      success: true,
      message: `Recharge of ₹${amount} processed successfully!`,
    };
  };

  const updateUserPoints = async (monthlyPoints: number, yearlyPoints?: number) => {
    try {
      const storedToken = await AsyncStorage.getItem("authToken");
      if (!storedToken || !user) {
        return {
          success: false,
          message: "Authentication required. Please login again.",
        };
      }

      const response = await fetch(`${backendUrl}api/users/update-points`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${storedToken}`,
        },
        body: JSON.stringify({
          userId: user._id,
          monthlyPoints,
          yearlyPoints,
        }),
      });

      const result = await response.json();
      if (result.success) {
        setUser((prev) => {
          if (!prev) return null;
          return {
            ...prev,
            monthlyPoints: result.monthlyPoints,
            yearlyPoints: result.yearlyPoints,
          };
        });
        await AsyncStorage.setItem(
          "user",
          JSON.stringify({
            ...user,
            monthlyPoints: result.monthlyPoints,
            yearlyPoints: result.yearlyPoints,
          })
        );
      }
      return result;
    } catch (error) {
      console.error("Update points error:", error);
      return {
        success: false,
        message: "Failed to update points",
      };
    }
  };

  const refreshUserData = async () => {
    try {
      const storedToken = await AsyncStorage.getItem("authToken");
      if (!storedToken) {
        return {
          success: false,
          message: "Authentication required. Please login again.",
        };
      }
      console.log("🔴 Refreshing user data from server...");
      const response = await fetch(`${backendUrl}api/auth/verify-token`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${storedToken}`,
          "Content-Type": "application/json",
        },
      });
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.user) {
          setUser(data.user);
          await AsyncStorage.setItem("user", JSON.stringify(data.user));
          console.log("✅ User data refreshed successfully:", {
            monthlyPoints: data.user.monthlyPoints,
            yearlyPoints: data.user.yearlyPoints,
          });
          return {
            success: true,
            message: "User data refreshed successfully",
            data: data.user,
          };
        }
      }
      return {
        success: false,
        message: "Failed to refresh user data",
      };
    } catch (error) {
      console.error("❌ Refresh user data error:", error);
      return {
        success: false,
        message: "Network error while refreshing user data",
      };
    }
  };

  const updateUserProfile = async (profileData: any) => {
    try {
      const storedToken = await AsyncStorage.getItem("authToken");
      if (!storedToken || !user) {
        return {
          success: false,
          message: "Authentication required. Please login again.",
        };
      }
      const formData = new FormData();
      if (profileData.name) formData.append("fullName", profileData.name);
      if (profileData.password)
        formData.append("password", profileData.password);
      if (profileData.phoneNumber)
        formData.append("phoneNumber", profileData.phoneNumber);
      if (profileData.dateOfBirth) {
        const dateOfBirth =
          profileData.dateOfBirth instanceof Date
            ? profileData.dateOfBirth
            : new Date(profileData.dateOfBirth);
        formData.append("dateOfBirth", dateOfBirth.toISOString().split("T")[0]);
      }
      if (profileData.age) formData.append("age", profileData.age.toString());
      if (profileData.adharNumber)
        formData.append("adharNumber", profileData.adharNumber);
      if (profileData.panCardNumber)
        formData.append("panCardNumber", profileData.panCardNumber);
      if (profileData.pinCode) formData.append("pinCode", profileData.pinCode);
      if (profileData.state) formData.append("state", profileData.state);
      if (profileData.city) formData.append("city", profileData.city);
      if (profileData.address) formData.append("address", profileData.address);
      if (profileData.dealerCode)
        formData.append("dealerCode", profileData.dealerCode);

      // Process profile photo
      if (profileData.profilePhoto) {
        const fileInfo = await FileSystem.getInfoAsync(profileData.profilePhoto);
        console.log("📸 Profile photo pre-processing check for update:", {
          exists: fileInfo.exists,
          uri: profileData.profilePhoto,
        });
        if (!fileInfo.exists) {
          console.error("❌ Profile photo does not exist before processing");
          return {
            success: false,
            message: "Profile image is no longer accessible. Please select it again.",
          };
        }
        const processedPhoto = await processImageForUpload(profileData.profilePhoto, "profile");
        if (!processedPhoto.success) {
          return processedPhoto;
        }
        formData.append("profilePhoto", processedPhoto.file);
      }

      // Process adhar card
      if (profileData.adharCard) {
        const fileInfo = await FileSystem.getInfoAsync(profileData.adharCard);
        console.log("📸 Adhar card pre-processing check for update:", {
          exists: fileInfo.exists,
          uri: profileData.adharCard,
        });
        if (!fileInfo.exists) {
          console.error("❌ Adhar card does not exist before processing");
          return {
            success: false,
            message: "Adhar card image is no longer accessible. Please select it again.",
          };
        }
        const processedAdhar = await processImageForUpload(profileData.adharCard, "adhar");
        if (!processedAdhar.success) return processedAdhar;
        formData.append("adharCard", processedAdhar.file);
      }

      // Process pan card
      if (profileData.panCard) {
        const fileInfo = await FileSystem.getInfoAsync(profileData.panCard);
        console.log("📸 Pan card pre-processing check for update:", {
          exists: fileInfo.exists,
          uri: profileData.panCard,
        });
        if (!fileInfo.exists) {
          console.error("❌ Pan card does not exist before processing");
          return {
            success: false,
            message: "Pan card image is no longer accessible. Please select it again.",
          };
        }
        const processedPan = await processImageForUpload(profileData.panCard, "pan");
        if (!processedPan.success) return processedPan;
        formData.append("panCard", processedPan.file);
      }

      // Process bank details
      if (profileData.bankDetails) {
        const fileInfo = await FileSystem.getInfoAsync(profileData.bankDetails);
        console.log("📸 Bank details pre-processing check for update:", {
          exists: fileInfo.exists,
          uri: profileData.bankDetails,
        });
        if (!fileInfo.exists) {
          console.error("❌ Bank details does not exist before processing");
          return {
            success: false,
            message: "Bank details image is no longer accessible. Please select it again.",
          };
        }
        const processedBank = await processImageForUpload(profileData.bankDetails, "bank");
        if (!processedBank.success) return processedBank;
        formData.append("bankDetails", processedBank.file);
      }

      const response = await axios.post(`${backendUrl}api/users/${user._id}`, formData, {
        headers: {
          Authorization: `Bearer ${storedToken}`,
          "Content-Type": "multipart/form-data",
        },
        timeout: 20000,
      });
      const result = response.data;
      if (result.success) {
        setUser(result.data.user);
        await AsyncStorage.setItem("user", JSON.stringify(result.data.user));
        await cleanupTempFiles(); // Clean up after successful upload
      }
      return result;
    } catch (error) {
      console.error("Update profile error:", error);
      return {
        success: false,
        message: "Failed to update profile",
      };
    }
  };

  const forgotPasswordSendOTP = async (phoneNumber: string) => {
    try {
      setIsLoading(true);
      console.log(
        "🚗‍♂️ AUTHCONTEXT: Sending forgot password OTP to WhatsApp:",
        phoneNumber
      );
      console.log(
        "🚗‍♂️ AUTHCONTEXT: Using Interakt WhatsApp service for forgot password OTP..."
      );
      const resultWhatsApp = await InteraktWhatsAppService.sendOTP(phoneNumber);
      if (resultWhatsApp.success) {
        console.log("✅ Forgot password OTP sent successfully via WhatsApp");
        console.log(
          "🔢 Generated OTP (for debugging):",
          resultWhatsApp.data?.otp
        );
        return {
          success: true,
          message:
            "Password reset code sent to your WhatsApp. Please check your messages.",
        };
      } else {
        console.error("❌ WhatsApp service failed:", resultWhatsApp.message);
        console.log("🔄 WhatsApp failed, trying backend API as fallback...");
        const response = await fetch(`${backendUrl}api/auth/forgot-password`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ phoneNumber }),
        });
        const result = await response.json();
        return result;
      }
    } catch (error) {
      console.error("Forgot password send OTP error:", error);
      return {
        success: false,
        message: "Network error. Please check your connection and try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const resetPasswordWithOTP = async (
    phoneNumber: string,
    otp: string,
    newPassword: string
  ) => {
    try {
      setIsLoading(true);
      console.log("🔍 AUTHCONTEXT: Resetting password for:", phoneNumber);
      console.log("🔄 Using backend verification directly...");
      const response = await fetch(`${backendUrl}api/auth/reset-password`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          phoneNumber: phoneNumber.trim(),
          otp: otp.trim(),
          newPassword,
        }),
      });
      const result = await response.json();
      if (!response.ok) {
        console.error("❌ Backend response error:", result);
        return {
          success: false,
          message: result.message || "Password reset failed",
        };
      }
      return result;
    } catch (error) {
      console.error("Reset password error:", error);
      return {
        success: false,
        message: "Network error. Please check your connection and try again.",
      };
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOtpRequest = async (phoneNumber: string, otp: string) => {
    try {
      setIsLoading(true);
      console.log(
        "🔍 AUTHCONTEXT: Verifying OTP for forgot password:",
        phoneNumber
      );
      console.log("🔍 Using Interakt WhatsApp service for verification...");
      let verificationResult = await InteraktWhatsAppService.verifyOTP(
        phoneNumber,
        otp
      );
      if (!verificationResult.success) {
        console.log(
          "🔄 Interakt verification failed, trying test service as fallback..."
        );
        verificationResult = await TestOTPService.verifyOTP(phoneNumber, otp);
      }
      if (verificationResult.success) {
        console.log("✅ OTP verified successfully for forgot password");
        return {
          success: true,
          message: "OTP verified successfully",
        };
      } else {
        console.error(
          "❌ OTP verification failed:",
          verificationResult.message
        );
        return {
          success: false,
          message:
            verificationResult.message || "Invalid OTP. Please try again.",
        };
      }
    } catch (error: any) {
      console.error("🚨 OTP verification error:", error);
      return {
        success: false,
        message: `Verification failed: ${error.message}`,
      };
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    login,
    register,
    sendOtpRequest,
    resendOtpRequest,
    verifyOtpAndRegister,
    forgotPasswordSendOTP,
    resetPasswordWithOTP,
    logout,
    isAuthenticated: !!user,
    addPoints,
    processQRCode,
    processRecharge,
    updateUserPoints,
    updateUserProfile,
    refreshUserData,
    verifyOtpRequest,
    takePhoto,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined)
    throw new Error("useAuth must be used within an AuthProvider");
  return context;
};