import React, { useState, useCallback } from 'react';
import {
  View, Text, TextInput, TouchableOpacity, StyleSheet,
  ScrollView, Alert, ActivityIndicator, KeyboardAvoidingView, Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import PhoneInputComponent from '@/components/PhoneInput';
import DatePickerInput from '@/components/DatePickerInput';
import PhotoUpload from '@/components/PhotoUpload';
import BasicPhotoUpload from '@/components/BasicPhotoUpload';
import { useAuth } from '@/contexts/AuthContext';
import { router } from 'expo-router';
import { Picker } from '@react-native-picker/picker';

export default function RegisterScreen() {
  const [form, setForm] = useState({
    fullName: '', password: '', dateOfBirth: null as Date | null,
    age: '', phoneNumber: '', adharNumber: '', panCardNumber: '',
    pinCode: '', state: '', city: '', address: '', dealerCode: '',
    profilePhoto: '', adharCard: '', panCard: '', bankDetails: '',
    role: 'Electrician'
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { sendOtpRequest } = useAuth();

  // Update function for form fields
  const update = useCallback((field: string, value: any) => {
    setForm(prev => ({ ...prev, [field]: typeof value === 'string' ? value.trim() : value }));
    if (errors[field]) setErrors(prev => ({ ...prev, [field]: '' }));
    if (field === 'dateOfBirth' && value instanceof Date) {
      const today = new Date();
      let age = today.getFullYear() - value.getFullYear();
      const m = today.getMonth() - value.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < value.getDate())) age--;
      setForm(prev => ({ ...prev, age: age.toString() }));
    }
  }, [errors]);

  const validate = () => {
    const e: Record<string, string> = {};
    if (!form.fullName.trim() || form.fullName.trim().length < 2 || form.fullName.trim().length > 100 || 
        !/^[a-zA-Z ]+$/.test(form.fullName)) {
      e.fullName = 'Full name must be 2-100 letters and spaces';
    }
    if (!form.password || form.password.length < 6) e.password = 'Password must be at least 6 characters';
    if (!form.dateOfBirth || !(form.dateOfBirth instanceof Date)) e.dateOfBirth = 'Valid date of birth is required';
    if (!form.age || +form.age < 18 || +form.age > 100) e.age = 'Age must be between 18-100';
    if (!form.phoneNumber || !/^[6-9]\d{9}$/.test(form.phoneNumber)) e.phoneNumber = 'Valid 10-digit Indian phone required';
    if (form.adharNumber && !/^\d{12}$/.test(form.adharNumber)) e.adharNumber = 'Aadhar must be 12 digits';
    if (form.panCardNumber && !/^[A-Z]{5}\d{4}[A-Z]$/.test(form.panCardNumber)) e.panCardNumber = 'Invalid PAN format';
    if (!form.pinCode || !/^\d{6}$/.test(form.pinCode)) e.pinCode = 'PIN must be 6 digits';
    if (!form.state.trim()) e.state = 'State is required';
    if (!form.city.trim()) e.city = 'City is required';
    if (!form.address.trim() || form.address.length < 10 || form.address.length > 500) e.address = 'Address must be 10-500 characters';
    if (!form.dealerCode.trim()) e.dealerCode = 'Dealer code is required';
    
    // Skip validation for optional bank details
    if (!form.bankDetails) {
      console.log('ℹ️ Bank details not provided - optional field');
    }

    console.log('🔍 Validation errors found:', e);
    setErrors(e);
    return Object.keys(e).length === 0;
  };

  const handleRegister = async () => {
    console.log('🔍 Validating form data...');
    console.log('📋 Current form state:', { ...form, password: '[HIDDEN]' });
    
    if (!validate()) {
      console.log('🚫 Validation failed:', errors);
      Alert.alert('Validation Error', 'Please correct the errors in the form.');
      return;
    }

    try {
      setIsSubmitting(true);
      console.log('🚀 Sending OTP request for:', form.phoneNumber);
      const res = await sendOtpRequest(form.phoneNumber);
      
      if (res.success) {
        // Create typed payload with all required and optional fields
        const payload: {
          fullName: string;
          password: string;
          dateOfBirth: Date;
          age: number;
          phoneNumber: string;
          adharNumber: string;
          panCardNumber: string;
          pinCode: string;
          state: string;
          city: string;
          address: string;
          dealerCode: string;
          profilePhoto: string;
          adharCard: string;
          panCard: string;
          role: string;
          bankDetails?: string;
        } = {
          fullName: form.fullName.trim(),
          password: form.password,
          dateOfBirth: form.dateOfBirth || new Date(),
          age: parseInt(form.age) || 0,
          phoneNumber: form.phoneNumber,
          adharNumber: form.adharNumber || '',
          panCardNumber: form.panCardNumber || '',
          pinCode: form.pinCode,
          state: form.state,
          city: form.city,
          address: form.address,
          dealerCode: form.dealerCode.toUpperCase(),
          profilePhoto: form.profilePhoto || '',
          adharCard: form.adharCard || '',
          panCard: form.panCard || '',
          role: form.role
        };
        
        // Handle bankDetails with Expo file system workaround
        if (form.bankDetails) {
          try {
            // For file URIs, convert to base64 to avoid temporary file issues
            if (form.bankDetails.startsWith('file://')) {
              console.log('ℹ️ Converting bank details file to base64');
              const fileInfo = await FileSystem.getInfoAsync(form.bankDetails);
              if (fileInfo.exists) {
                const base64 = await FileSystem.readAsStringAsync(form.bankDetails, {
                  encoding: FileSystem.EncodingType.Base64
                });
                payload.bankDetails = `data:image/jpeg;base64,${base64}`;
              } else {
                console.log('⚠️ Bank details file no longer exists');
                throw new Error('Bank details image no longer accessible');
              }
            } 
            // Keep http/https URLs as-is
            else if (form.bankDetails.startsWith('http')) {
              payload.bankDetails = form.bankDetails;
            }
          } catch (error: unknown) {
            console.error('Bank details processing failed:', error);
            throw new Error('Failed to process bank details image');
          }
        }

        console.log('🔍 Registration payload details:', { 
          ...payload,
          password: '[HIDDEN]',
          dateOfBirth: payload.dateOfBirth?.toISOString(),
          fieldsPresent: {
            fullName: !!payload.fullName, 
            phoneNumber: !!payload.phoneNumber,
            password: !!payload.password,
            dateOfBirth: !!payload.dateOfBirth,
            age: !!payload.age,
            pinCode: !!payload.pinCode,
            state: !!payload.state,
            city: !!payload.city,
            address: !!payload.address,
            dealerCode: !!payload.dealerCode
          }
        });
        console.log('📸 Profile photo in payload:', {
          profilePhoto: payload.profilePhoto,
          hasProfilePhoto: !!payload.profilePhoto,
          profilePhotoLength: payload.profilePhoto?.length
        });

        // Debug the payload being sent
        const requestPayload = {
          ...payload,
          dateOfBirth: payload.dateOfBirth.toISOString() // Ensure proper date format
        };
        
        console.log('📤 Sending payload to backend:', requestPayload);
        
        // Convert to FormData for backend compatibility
        const formData = new FormData();
        
        // Skip non-file fields - they'll be added with their processed files
        const nonFileFields = ['bankDetails', 'password']; // Fields to skip
        Object.entries(requestPayload).forEach(([key, value]) => {
          // Skip if it's a non-file field or already processed
          if (!nonFileFields.includes(key) && value !== undefined) {
            formData.append(key, String(value));
          }
        });

        // Optimize file uploads - compress images and set max sizes (5MB per file)
        const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
        
        const processFile = async (uri: string, fieldName: string) => {
          try {
            const fileInfo = await FileSystem.getInfoAsync(uri);
            if (!fileInfo.exists) {
              throw new Error(`${fieldName} file not found`);
            }

            // Compress the image first
            const compressedImage = await ImageManipulator.manipulateAsync(
              uri,
              [{ resize: { width: 800 } }],
              { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
            );

            // Check final file size
            const response = await fetch(compressedImage.uri);
            const blob = await response.blob();
            
            if (blob.size > MAX_FILE_SIZE) {
              console.warn(`${fieldName} file too large after compression (${(blob.size/1024/1024).toFixed(2)}MB)`);
              throw new Error(`${fieldName} exceeds 5MB limit`);
            }

            return {
              uri: compressedImage.uri,
              name: uri.split('/').pop() || `${fieldName}.jpg`,
              type: 'image/jpeg'
            };
          } catch (error) {
            console.error(`Error processing ${fieldName}:`, error);
            throw error;
          }
        };

        // Handle file uploads with size validation
        const fileFields = {
          profilePhoto: form.profilePhoto,
          adharCard: form.adharCard,
          panCard: form.panCard,
          bankDetails: form.bankDetails
        };

        for (const [fieldName, uri] of Object.entries(fileFields)) {
          if (uri) {
            try {
              const processedFile = await processFile(uri, fieldName);
              formData.append(fieldName, processedFile as unknown as Blob);
            } catch (error) {
              console.error(`Error with ${fieldName}:`, error);
              Alert.alert('Upload Error', `Failed to process ${fieldName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
              throw error;
            }
          }
        }

        console.log('📤 Prepared FormData:', formData);

        // Send registration request directly to backend
        try {
          const response = await fetch(`${process.env.EXPO_PUBLIC_API_URL}/api/auth/register`, {
            method: 'POST',
            body: formData,
            headers: {
              'Accept': 'application/json',
            },
          });

          let data;
          const responseText = await response.text();
          
          // First check if response looks like HTML
          if (responseText.startsWith('<!DOCTYPE html>') || 
              responseText.startsWith('<html>')) {
            console.error('Received HTML response:', responseText.substring(0, 200));
            throw new Error('Server error occurred (check server logs)');
          }

          try {
            data = responseText ? JSON.parse(responseText) : {};
          } catch (jsonError) {
            console.error('Failed to parse response:', {
              error: jsonError,
              responseStatus: response.status,
              responseText: responseText.substring(0, 200)
            });
            throw new Error(`Server returned invalid data (status ${response.status})`);
          }

          if (response.ok) {
            router.push({
              pathname: '/verify-otp',
              params: {
                phoneNumber: form.phoneNumber,
                context: 'register'
              }
            });
          } else {
            throw new Error(data.message || 'Registration failed');
          }
        } catch (error: unknown) {
          console.error('Registration API error:', error);
          Alert.alert('Registration Error', 
            error instanceof Error ? error.message : 'An unknown error occurred');
        }
      } else {
        Alert.alert('Error', res.message || 'Failed to send OTP');
      }
    } catch (error) {
      console.error('Registration error:', error);
      Alert.alert('Error', 'Failed to send OTP. Please check your network and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView contentContainerStyle={styles.content}>
          <Text style={styles.title}>Create Account</Text>

          <BasicPhotoUpload
            title="Profile Photo*"
            value={form.profilePhoto}
            onPhotoSelected={uri => update('profilePhoto', uri )}
            error={errors.profilePhoto}
            required={true}
            cameraOnly={true}
          />

          <Field
            label="Full Name*"
            value={form.fullName}
            error={errors.fullName}
            onChange={(t: string) => update('fullName', t)}
            placeholder="Enter your full name"
          />
          <Field
            label="Password*"
            value={form.password}
            error={errors.password}
            onChange={(t: string) => update('password', t)}
            secure
            placeholder="Enter your password"
          />

          <DatePickerInput
            date={form.dateOfBirth}
            onChange={d => update('dateOfBirth', d)}
            error={errors.dateOfBirth}
          />
          <Field
            label="Age"
            value={form.age}
            error={errors.age}
            editable={false}
            placeholder="Calculated automatically"
          />

          <Field
            label="Phone Number*"
            value={form.phoneNumber}
            error={errors.phoneNumber}
            onChange={(t: string) => update('phoneNumber', t)}
            keyboardType="phone-pad"
            placeholder="Enter your phone number"
          />

          <Field
            label="Aadhar Number* "
            value={form.adharNumber}
            error={errors.adharNumber}
            onChange={(t: string) => update('adharNumber', t)}
            keyboardType="numeric"
            required={true}
            placeholder="Enter 12-digit Aadhar number"
          />
          <PhotoUpload
            title="Upload Aadhar Photo "
            value={form.adharCard}
            onPhotoSelected={uri => update('adharCard', uri)}
          />

          <Field
            label="PAN Card "
            value={form.panCardNumber}
            error={errors.panCardNumber}
            onChange={(t: string) => update('panCardNumber', t)}
            autoCapitalize="characters"
            placeholder="Enter PAN number"
          />
          <PhotoUpload
            title="Upload PAN Photo "
            value={form.panCard}
            onPhotoSelected={uri => update('panCard', uri)}
          />

          <PhotoUpload
            title="Upload Bank Details (Optional)"
            value={form.bankDetails}
            onPhotoSelected={uri => update('bankDetails', uri)}
          />

          <Field
            label="PIN Code*"
            value={form.pinCode}
            error={errors.pinCode}
            onChange={(t: string) => update('pinCode', t)}
            keyboardType="numeric"
            placeholder="Enter 6-digit PIN code"
          />
          <Field
            label="State*"
            value={form.state}
            error={errors.state}
            onChange={(t: string) => update('state', t)}
            placeholder="Enter your state"
          />
          <Field
            label="City*"
            value={form.city}
            error={errors.city}
            onChange={(t: string) => update('city', t)}
            placeholder="Enter your city"
          />
          <Field
            label="Address*"
            value={form.address}
            error={errors.address}
            onChange={(t: string) => update('address', t)}
            multiline
            numberOfLines={3}
            placeholder="Enter your full address"
          />

          <Field
            label="Dealer Code"
            value={form.dealerCode}
            required={false}
            error={errors.dealerCode}
            onChange={(t: string) => update('dealerCode', t)}
            placeholder="Enter dealer code"
          />

          <View style={styles.field}>
            <Text style={styles.label}>Role*</Text>
            <Picker
              selectedValue={form.role}
              onValueChange={(val) => update('role', val)}
              style={styles.input}
            >
              <Picker.Item label="Electrician" value="Electrician" />
              <Picker.Item label="Distributor" value="Distributor" />
            </Picker>
          </View>

          <TouchableOpacity
            style={[styles.button, isSubmitting && styles.disabledButton]}
            onPress={handleRegister}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Register</Text>
            )}
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

function Field({
  label, value, onChange, error, secure = false,
  keyboardType = 'default', autoCapitalize = 'none',
  multiline = false, numberOfLines = 1, placeholder = '', editable = true
}: any) {
  return (
    <View style={styles.field}>
      <Text style={styles.label}>{label}</Text>
      <TextInput
        style={[styles.input, error && styles.errorInput]}
        value={value}
        onChangeText={onChange}
        secureTextEntry={secure}
        keyboardType={keyboardType}
        autoCapitalize={autoCapitalize}
        multiline={multiline}
        numberOfLines={numberOfLines}
        placeholder={placeholder}
        placeholderTextColor="#999"
        editable={editable}
      />
      {error && <Text style={styles.error}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f7f9fb' },
  content: { padding: 20 },
  title: { fontSize: 26, fontWeight: '700', marginBottom: 24, textAlign: 'center', color: '#333' },
  field: { marginBottom: 16 },
  label: { fontSize: 16, fontWeight: '600', marginBottom: 6, color: '#444' },
  input: { backgroundColor: '#fff', padding: 12, borderRadius: 8, borderWidth: 1, borderColor: '#ccc', fontSize: 16 },
  errorInput: { borderColor: 'red' },
  error: { color: 'red', marginTop: 4, fontSize: 14 },
  button: { backgroundColor: '#0066cc', padding: 16, borderRadius: 8, alignItems: 'center', marginTop: 20 },
  disabledButton: { backgroundColor: '#ccc' },
  buttonText: { color: '#fff', fontSize: 18, fontWeight: '600' }
});
