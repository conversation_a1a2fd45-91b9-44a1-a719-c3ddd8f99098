import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import { validationResult } from "express-validator";
import User from "../Models/user.js";
import Redis from "ioredis";
import asyncRetry from "async-retry";
import sanitize from "sanitize-html";
import intrektService from "../services/intrektService.js";

// Disable Redis for now (using memory storage only)
let redis = null;
console.log("🔧 Using memory storage for OTP (Redis disabled)");

// In-memory storage fallback
const memoryStorage = new Map();

// OTP Configuration

// OTP Helper Functions
const generateOTP = () =>
  Math.floor(100000 + Math.random() * 900000).toString();

const storeOTP = async (phoneNumber, otp, type = "registration") => {
  const key =
    type === "forgot_password"
      ? `otp:forgot_password:${phoneNumber}`
      : `otp:${phoneNumber}`;
  console.log("💾 Storing OTP:", { key, otp, type });

  try {
    // Always use memory storage for now (Redis issues)
    const expiresAt = Date.now() + 15 * 60 * 1000;
    memoryStorage.set(key, { otp, expiresAt });
    console.log("✅ OTP stored in memory:", {
      key,
      otp,
      expiresAt: new Date(expiresAt),
    });

    // Auto cleanup after expiry
    setTimeout(() => {
      memoryStorage.delete(key);
      console.log("🗑️ OTP expired and removed:", key);
    }, 15 * 60 * 1000);

    // Try Redis as backup (but don't fail if it doesn't work)
    if (redis) {
      try {
        await redis.set(key, otp, "EX", 15 * 60);
        console.log("✅ OTP also stored in Redis as backup");
      } catch (redisError) {
        console.log(
          "⚠️ Redis backup failed (using memory storage):",
          redisError.message
        );
      }
    }
  } catch (error) {
    console.error("❌ Error storing OTP:", error);
    throw error;
  }
};

const verifyStoredOTP = async (
  phoneNumber,
  enteredOTP,
  type = "registration"
) => {
  const key =
    type === "forgot_password"
      ? `otp:forgot_password:${phoneNumber}`
      : `otp:${phoneNumber}`;
  console.log("🔍 Verifying OTP for phone:", phoneNumber);
  console.log("🔍 Storage key:", key);
  console.log("🔍 Entered OTP:", enteredOTP);
  console.log("🔍 OTP type:", type);

  try {
    let storedOTP = null;

    // Always check memory storage first (Redis has issues)
    const stored = memoryStorage.get(key);
    console.log("🔍 Memory storage check:", stored);
    console.log(
      "🔍 Current memory storage keys:",
      Array.from(memoryStorage.keys())
    );

    if (stored && Date.now() < stored.expiresAt) {
      storedOTP = stored.otp;
      console.log("✅ Valid stored OTP found in memory:", storedOTP);
    } else if (stored) {
      console.log("⏰ Expired OTP found, cleaning up");
      memoryStorage.delete(key);
    } else {
      console.log("❌ No OTP found in memory storage");

      // Try Redis as fallback (but handle errors gracefully)
      if (redis) {
        try {
          storedOTP = await redis.get(key);
          console.log("🔍 Redis fallback OTP:", storedOTP);
        } catch (redisError) {
          console.log("⚠️ Redis fallback failed:", redisError.message);
        }
      }
    }

    if (!storedOTP) {
      console.log("❌ No valid OTP found in any storage");
      return { success: false, message: "OTP not found or expired" };
    }

    console.log("🔍 Comparing OTPs:", {
      stored: storedOTP,
      entered: enteredOTP,
    });

    if (storedOTP === enteredOTP) {
      // Remove OTP after successful verification
      memoryStorage.delete(key);
      console.log("✅ OTP verified successfully and removed from storage");

      // Also try to remove from Redis if available
      if (redis) {
        try {
          await redis.del(key);
          console.log("✅ OTP also removed from Redis");
        } catch (redisError) {
          console.log(
            "⚠️ Redis cleanup failed (not critical):",
            redisError.message
          );
        }
      }

      return { success: true, message: "OTP verified successfully" };
    }

    console.log("❌ OTP mismatch");
    return { success: false, message: "Invalid OTP" };
  } catch (error) {
    console.error("❌ Error verifying OTP:", error);
    return { success: false, message: "Error verifying OTP" };
  }
};

// OTP service functions

const generateToken = (id) => {
  if (!process.env.JWT_SECRET) {
    throw new Error("JWT_SECRET is not defined");
  }
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || "7d",
  });
};

export const register = async (req, res) => {
  try {
    console.log("📝 Register function called with data:", req.body);
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res
        .status(400)
        .json({
          success: false, 
          message: "Validation failed",
          errors: errors.array(),
        });
    }

    const {
      fullName,
      phoneNumber,
      password,
      dateOfBirth,
      age,
      adharNumber,
      panCardNumber, 
      pinCode,
      state,
      city,
      address,
      dealerCode,
      role,
    } = req.body;

    // Add points service validation
    try {
      PointsService.validateUserId(phoneNumber); // Using phone as user ID
      PointsService.validatePoints(500, 'add'); // Minimum points check
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: error.message,
        field: 'validation'
      });
    }

    // Sanitize inputs
    const sanitizedData = {
      fullName: sanitize(fullName),
      phoneNumber: sanitize(phoneNumber),
      password,
      dateOfBirth: new Date(sanitize(dateOfBirth)),
      age: parseInt(sanitize(age)),
      adharNumber: adharNumber ? sanitize(adharNumber) : undefined,
      panCardNumber: panCardNumber ? sanitize(panCardNumber) : undefined,
      pinCode: sanitize(pinCode),
      state: sanitize(state),
      city: sanitize(city),
      address: sanitize(address),
      dealerCode: sanitize(dealerCode).toUpperCase(),
      role: sanitize(role || "Electrician"),
    };

    // Check for existing users with comprehensive field validation
    const conflictChecks = [
      {
        field: "phoneNumber",
        value: sanitizedData.phoneNumber,
        message: "Phone number already registered",
      },
      {
        field: "adharNumber",
        value: sanitizedData.adharNumber,
        message: "Adhar number already registered",
      },
      {
        field: "panCardNumber",
        value: sanitizedData.panCardNumber,
        message: "PAN card number already registered",
      },
      {
        field: "dealerCode",
        value: sanitizedData.dealerCode,
        message: "Dealer code already in use",
      },
    ];

    for (const check of conflictChecks) {
      if (check.value) {
        // Only check if the field has a value
        const existingUser = await asyncRetry(
          async () =>
            await User.findOne({ [check.field]: check.value }).maxTimeMS(15000),
          { retries: 3, minTimeout: 1000, factor: 2 }
        );

        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: check.message,
            field: check.field,
            conflictingValue: check.value,
          });
        }
      }
    }

    // Debug: Log what files are received
    console.log("📁 Files received:", req.files);
    console.log(
      "📁 Files keys:",
      req.files ? Object.keys(req.files) : "No files"
    );
    console.log("📁 ProfilePhoto check:", req.files?.profilePhoto);
    console.log("📁 Request body keys:", Object.keys(req.body));

    // Remove profile photo requirement - handled by frontend validation
    // Enhanced validation for file uploads
    // let hasProfilePhoto = false;

    // if (req.files && req.files.profilePhoto) {
    //   hasProfilePhoto = true;
    //   console.log('✅ Profile photo found in req.files.profilePhoto');
    // } else if (req.files && Array.isArray(req.files.profilePhoto)) {
    //   hasProfilePhoto = req.files.profilePhoto.length > 0;
    //   console.log('✅ Profile photo found as array:', req.files.profilePhoto.length);
    // } else if (req.file && req.file.fieldname === 'profilePhoto') {
    //   hasProfilePhoto = true;
    //   console.log('✅ Profile photo found in req.file');
    // }

    // if (!hasProfilePhoto) {
    //   console.error('❌ Profile photo validation failed:', {
    //     hasFiles: !!req.files,
    //     hasFile: !!req.file,
    //     filesKeys: req.files ? Object.keys(req.files) : [],
    //     fileFieldname: req.file?.fieldname,
    //     hasProfilePhoto: false
    //   });
    //   return res.status(400).json({
    //     success: false,
    //     message: 'Profile photo is required',
    //     debug: {
    //       hasFiles: !!req.files,
    //       filesKeys: req.files ? Object.keys(req.files) : [],
    //       hasFile: !!req.file
    //     }
    //   });
    // }

    // Validate file types and sizes
    const allowedTypes = ["image/jpeg", "image/png"];
    const maxSize = 5 * 1024 * 1024; // 5MB
    for (const fileType of [
      "profilePhoto",
      "adharCard",
      "panCard",
      "bankDetails",
    ]) {
      if (
        req.files[fileType] &&
        !allowedTypes.includes(req.files[fileType][0].mimetype)
      ) {
        return res
          .status(400)
          .json({ success: false, message: `${fileType} must be JPEG or PNG` });
      }
      if (req.files[fileType] && req.files[fileType][0].size > maxSize) {
        return res
          .status(400)
          .json({ success: false, message: `${fileType} size exceeds 5MB` });
      }
    }

    // Prepare user data
    const userData = {
      ...sanitizedData,
      profilePhoto: req.files?.profilePhoto
        ? req.files.profilePhoto[0].path
        : undefined,
      adharCard: req.files.adharCard ? req.files.adharCard[0].path : undefined,
      panCard: req.files.panCard ? req.files.panCard[0].path : undefined,
      bankDetails: req.files.bankDetails
        ? req.files.bankDetails[0].path
        : undefined,
    };

    // Hash password
    // userData.password = await bcrypt.hash(password, 10);

    // Create user with transaction
    const session = await User.startSession();
    try {
      await session.withTransaction(async () => {
        const user = await User.create([userData], { session });
        return user[0];
      });
      const user = await User.findOne({
        phoneNumber: sanitizedData.phoneNumber,
      });
      const token = generateToken(user._id);

      res.status(201).json({
        success: true,
        message: "User registered successfully",
        data: {
          user: {
            id: user._id,
            fullName: user.fullName,
            phoneNumber: user.phoneNumber,
            role: user.role,
            status: user.status,
            isVerified: user.isVerified,
            monthlyPoints: user.monthlyPoints,
            yearlyPoints: user.yearlyPoints,
          },
          token,
        },
      });
    } finally {
      session.endSession();
    }
  } catch (error) {
    console.error("Register error:", error);
    if (error.message.includes("timeout")) {
      return res
        .status(503)
        .json({ success: false, message: "Database timeout" });
    }
    if (error.code === 11000) {
      // Extract the field name from the duplicate key error
      const duplicateField = Object.keys(error.keyPattern || {})[0];
      const fieldMessages = {
        phoneNumber: "Phone number already registered",
        adharNumber: "Adhar number already registered",
        panCardNumber: "PAN card number already registered",
        dealerCode: "Dealer code already in use",
      };

      const message = fieldMessages[duplicateField] || error.err;
      return res.status(400).json({
        success: false,
        message,
        field: duplicateField,
        error: "DUPLICATE_KEY",
      });
    }
    res.status(500).json({
      success: false,
      message: "Server error during registration",
      ...(process.env.NODE_ENV === "development" && { error: error.message }),
    });
  }
};

export const login = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res
        .status(400)
        .json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
    }

    const { phoneNumber, password } = req.body;
    console.log("listen request ", phoneNumber, password);
    const sanitizedPhone = sanitize(phoneNumber);

    const user = await asyncRetry(
      async () =>
        await User.findOne({ phoneNumber: sanitizedPhone })
          .select("+password")
          .maxTimeMS(15000),
      { retries: 3, minTimeout: 1000, factor: 2 }
    );

    console.log("🔍 User lookup result:", {
      phoneNumber: sanitizedPhone,
      userFound: !!user,
      userId: user?._id,
    });

    if (!user || !(await user.comparePassword(password))) {
      console.log("❌ Login failed:", {
        userExists: !!user,
        passwordCheck: user ? "failed" : "skipped",
      });
      return res
        .status(401)
        .json({ success: false, message: "Invalid credentials" });
    }

    console.log("🔍 Login - User found with points:", {
      id: user._id,
      fullName: user.fullName,
      monthlyPoints: user.monthlyPoints,
      yearlyPoints: user.yearlyPoints,
    });

    const token = generateToken(user._id);
    res.status(200).json({
      success: true,
      message: "Login successful",
      data: {
        user: {
          id: user._id,
          fullName: user.fullName,
          phoneNumber: user.phoneNumber,
          role: user.role,
          status: user.status,
          isVerified: user.isVerified,
          dealerCode: user.dealerCode,
          address: user.address,
          age: user.age,
          dateOfBirth: user.dateOfBirth,
          pinCode: user.pinCode,
          state: user.state,
          city: user.city,
          adharNumber: user.adharNumber,
          panCardNumber: user.panCardNumber,
          profilePhoto: user.profilePhoto,
          adharCard: user.adharCard,
          panCard: user.panCard,
          bankDetails: user.bankDetails,
          monthlyPoints: user.monthlyPoints,
          yearlyPoints: user.yearlyPoints,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
        token,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during login",
      ...(process.env.NODE_ENV === "development" && { error: error.message }),
    });
  }
};

export const logout = async (_req, res) => {
  res.status(200).json({ success: true, message: "Logout successful" });
};

// Test endpoint to debug user data fetching
export const testUserData = async (req, res) => {
  try {
    const user = req.user;
    console.log("🧪 Test endpoint - User from middleware:", {
      id: user._id,
      fullName: user.fullName,
      monthlyPoints: user.monthlyPoints,
      yearlyPoints: user.yearlyPoints,
    });

    // Fetch fresh data from database
    const freshUser = await User.findById(user._id);
    console.log("🧪 Test endpoint - Fresh user from DB:", {
      id: freshUser._id,
      fullName: freshUser.fullName,
      monthlyPoints: freshUser.monthlyPoints,
      yearlyPoints: freshUser.yearlyPoints,
    });

    res.status(200).json({
      success: true,
      message: "User data test",
      data: {
        middlewareUser: {
          id: user._id,
          fullName: user.fullName,
          monthlyPoints: user.monthlyPoints,
          yearlyPoints: user.yearlyPoints,
        },
        freshUser: {
          id: freshUser._id,
          fullName: freshUser.fullName,
          monthlyPoints: freshUser.monthlyPoints,
          yearlyPoints: freshUser.yearlyPoints,
        },
      },
    });
  } catch (error) {
    console.error("❌ Test endpoint error:", error);
    res.status(500).json({
      success: false,
      message: "Test endpoint error",
      error: error.message,
    });
  }
};

export const verifyToken = async (req, res) => {
  try {
    // The authenticateToken middleware already verified the token and set req.user
    const user = req.user;

    console.log("🔍 Verifying token for user:", user._id);
    console.log("🔍 User points data from middleware:", {
      monthlyPoints: user.monthlyPoints,
      yearlyPoints: user.yearlyPoints,
    });

    // Double-check by fetching fresh user data from database to ensure we have latest points
    const freshUser = await User.findById(user._id).select("-password");

    if (!freshUser) {
      console.error("❌ User not found in database during token verification");
      return res.status(404).json({
        success: false,
        message: "User not found in database",
      });
    }

    console.log("✅ Fresh user data from DB:", {
      monthlyPoints: freshUser.monthlyPoints,
      yearlyPoints: freshUser.yearlyPoints,
    });

    res.status(200).json({
      success: true,
      message: "Token is valid",
      user: {
        _id: freshUser._id,
        fullName: freshUser.fullName,
        phoneNumber: freshUser.phoneNumber,
        role: freshUser.role,
        address: freshUser.address,
        age: freshUser.age,
        dateOfBirth: freshUser.dateOfBirth,
        profilePhoto: freshUser.profilePhoto,
        adharCard: freshUser.adharCard,
        panCard: freshUser.panCard,
        bankDetails: freshUser.bankDetails,
        status: freshUser.status,
        isVerified: freshUser.isVerified,
        dealerCode: freshUser.dealerCode,
        monthlyPoints: freshUser.monthlyPoints || 0,
        yearlyPoints: freshUser.yearlyPoints || 0,
        pinCode: freshUser.pinCode,
        state: freshUser.state,
        city: freshUser.city,
        adharNumber: freshUser.adharNumber,
        panCardNumber: freshUser.panCardNumber,
        createdAt: freshUser.createdAt,
        updatedAt: freshUser.updatedAt,
      },
    });
  } catch (error) {
    console.error("❌ Token verification error:", error);
    console.error("❌ Error stack:", error.stack);
    res.status(500).json({
      success: false,
      message: "Server error during token verification",
      ...(process.env.NODE_ENV === "development" && { error: error.message }),
    });
  }
};

export const sendOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res
        .status(400)
        .json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
    }

    const { phoneNumber } = req.body;
    const sanitizedPhone = sanitize(phoneNumber);

    // Rate limiting (skip if Redis not available)
    if (redis) {
      const rateLimitKey = `otp:rate:${sanitizedPhone}`;
      try {
        const attempts = await redis.get(rateLimitKey);
        if (attempts && parseInt(attempts) >= 5) {
          return res
            .status(429)
            .json({
              success: false,
              message: "Too many OTP requests. Try again later.",
            });
        }
      } catch (redisError) {
        console.warn("Redis rate limit check failed:", redisError.message);
      }
    }

    const otp = generateOTP();
    await storeOTP(sanitizedPhone, otp);

    // Try to send OTP via Intrekt WhatsApp first
    console.log("🚀 Attempting to send OTP via Intrekt WhatsApp...");
    const intrektResult = await intrektService.sendOTP(sanitizedPhone, otp);

    if (intrektResult.success) {
      console.log("✅ OTP sent successfully via Intrekt WhatsApp");

      // Update rate limit (skip if Redis not available)
      if (redis) {
        try {
          const rateLimitKey = `otp:rate:${sanitizedPhone}`;
          await redis.incr(rateLimitKey);
          await redis.expire(rateLimitKey, 24 * 60 * 60); // 24 hours expiry
        } catch (redisError) {
          console.warn("Redis rate limit update failed:", redisError.message);
        }
      }

      return res.status(200).json({
        success: true,
        message: "OTP sent successfully via WhatsApp",
        data: {
          to: sanitizedPhone,
          method: "whatsapp",
          provider: "intrekt",
          // Include test OTP in development mode only
          ...(process.env.NODE_ENV !== "production" && { testOtp: otp }),
        },
      });
    } else {
      console.warn(
        "⚠️ Intrekt WhatsApp failed, falling back to console logging"
      );
      console.log("📱 Generated OTP for testing (Intrekt failed):", otp);

      // Update rate limit (skip if Redis not available)
      if (redis) {
        try {
          const rateLimitKey = `otp:rate:${sanitizedPhone}`;
          await redis.incr(rateLimitKey);
          await redis.expire(rateLimitKey, 24 * 60 * 60); // 24 hours expiry
        } catch (redisError) {
          console.warn("Redis rate limit update failed:", redisError.message);
        }
      }

      return res.status(200).json({
        success: true,
        message: "OTP sent successfully (fallback mode)",
        data: {
          to: sanitizedPhone,
          method: "console",
          provider: "fallback",
          intrektError: intrektResult.message,
          // Include test OTP in development mode only
          ...(process.env.NODE_ENV !== "production" && { testOtp: otp }),
        },
      });
    }
  } catch (error) {
    console.error("❌ Send OTP error:", error);
    console.error("❌ Error stack:", error.stack);
    res
      .status(500)
      .json({ success: false, message: `Server error: ${error.message}` });
  }
};

export const verifyOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res
        .status(400)
        .json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
    }

    const { phoneNumber, otp } = req.body;
    const verificationResult = await verifyStoredOTP(
      sanitize(phoneNumber),
      sanitize(otp)
    );

    res.status(verificationResult.success ? 200 : 400).json(verificationResult);
  } catch (error) {
    console.error("❌ Verify OTP error:", error);
    console.error("❌ Error stack:", error.stack);
    res
      .status(500)
      .json({ success: false, message: `Server error: ${error.message}` });
  }
};

export const resendOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: errors.array(),
      });
    }

    const { phoneNumber } = req.body;
    const sanitizedPhone = sanitize(phoneNumber);

    // Rate limiting
    if (redis) {
      const rateLimitKey = `otp:rate:${sanitizedPhone}`;
      try {
        const attempts = await redis.get(rateLimitKey);
        if (attempts && parseInt(attempts) >= 5) {
          return res.status(429).json({
            success: false,
            message: "Too many OTP requests. Try again later.",
          });
        }
      } catch (rateErr) {
        console.warn("Redis rate limit check failed:", rateErr.message);
      }
    }

    const otp = generateOTP();
    await storeOTP(sanitizedPhone, otp);

    // Try to resend OTP via Intrekt WhatsApp first
    console.log("🚀 Attempting to resend OTP via Intrekt WhatsApp...");
    const intrektResult = await intrektService.sendOTP(sanitizedPhone, otp);

    if (intrektResult.success) {
      console.log("✅ OTP resent successfully via Intrekt WhatsApp");

      // Update rate limit counter
      if (redis) {
        try {
          const rateLimitKey = `otp:rate:${sanitizedPhone}`;
          await redis.incr(rateLimitKey);
          await redis.expire(rateLimitKey, 24 * 60 * 60); // 24 hours
        } catch (rateUpdateErr) {
          console.warn(
            "Redis rate limit update failed:",
            rateUpdateErr.message
          );
        }
      }

      return res.status(200).json({
        success: true,
        message: "OTP resent successfully via WhatsApp",
        data: {
          to: sanitizedPhone,
          method: "whatsapp",
          provider: "intrekt",
          ...(process.env.NODE_ENV !== "production" && { testOtp: otp }),
        },
      });
    } else {
      console.warn(
        "⚠️ Intrekt WhatsApp failed for resend, falling back to console logging"
      );
      console.log("📱 OTP resent (Intrekt failed):", otp);

      // Update rate limit counter
      if (redis) {
        try {
          const rateLimitKey = `otp:rate:${sanitizedPhone}`;
          await redis.incr(rateLimitKey);
          await redis.expire(rateLimitKey, 24 * 60 * 60); // 24 hours
        } catch (rateUpdateErr) {
          console.warn(
            "Redis rate limit update failed:",
            rateUpdateErr.message
          );
        }
      }

      return res.status(200).json({
        success: true,
        message: "OTP resent successfully (fallback mode)",
        data: {
          to: sanitizedPhone,
          method: "console",
          provider: "fallback",
          intrektError: intrektResult.message,
          ...(process.env.NODE_ENV !== "production" && { testOtp: otp }),
        },
      });
    }
  } catch (error) {
    console.error("❌ Resend OTP error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error while resending OTP",
      ...(process.env.NODE_ENV === "development" && { error: error.message }),
    });
  }
};

// Forgot Password - Send OTP for password reset
export const forgotPasswordSendOTP = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res
        .status(400)
        .json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
    }

    const { phoneNumber } = req.body;
    const sanitizedPhone = sanitize(phoneNumber);

    // Check if user exists
    const user = await asyncRetry(
      async () =>
        await User.findOne({ phoneNumber: sanitizedPhone }).maxTimeMS(15000),
      { retries: 3, minTimeout: 1000, factor: 2 }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "No account found with this phone number",
      });
    }

    // Rate limiting
    if (redis) {
      const rateLimitKey = `forgot:rate:${sanitizedPhone}`;
      try {
        const attempts = await redis.get(rateLimitKey);
        if (attempts && parseInt(attempts) >= 3) {
          return res.status(429).json({
            success: false,
            message: "Too many password reset requests. Try again later.",
          });
        }
      } catch (redisError) {
        console.warn("Redis rate limit check failed:", redisError.message);
      }
    }

    const otp = generateOTP();
    await storeOTP(sanitizedPhone, otp, "forgot_password");

    const message = `Your FASTAGCAB password reset code is ${otp}. Valid for 15 minutes. Do not share this code.`;

    // Update rate limit counter
    if (redis) {
      try {
        const rateLimitKey = `forgot:rate:${sanitizedPhone}`;
        await redis.incr(rateLimitKey);
        await redis.expire(rateLimitKey, 24 * 60 * 60); // 24 hours expiry
      } catch (redisError) {
        console.warn("Redis rate limit update failed:", redisError.message);
      }
    }

    console.log("📱 Password reset OTP generated:", otp);

    return res.status(200).json({
      success: true,
      message: "Password reset code sent to your phone number",
      data: {
        to: sanitizedPhone,
        ...(process.env.NODE_ENV !== "production" && { testOtp: otp }),
      },
    });
  } catch (error) {
    console.error("❌ Forgot password send OTP error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error while sending password reset code",
      ...(process.env.NODE_ENV === "development" && { error: error.message }),
    });
  }
};

// Verify OTP and Reset Password
export const resetPasswordWithOTP = async (req, res) => {
  try {
    console.log("🔍 Reset password request received", req.body);
    const { phoneNumber, otp, newPassword } = req.body;
    // const errors = validationResult(req);
    // if (!errors.isEmpty()) {
    //   return res.status(400).json({ success: false, message: 'Validation failed', errors: errors.array() });
    // }

    // const { phoneNumber, otp, newPassword } = req.body;
    const sanitizedPhone = sanitize(phoneNumber);
    const sanitizedOtp = sanitize(otp);

    // Verify OTP
    // const verificationResult = await verifyStoredOTP(sanitizedPhone, sanitizedOtp, 'forgot_password');

    // if (!verificationResult.success) {
    //   return res.status(400).json(verificationResult);
    // }

    // Find user and update password
    const user = await User.findOne({ phoneNumber });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    console.log("🔍 Resetting password for:", user);

    // Update password (will be hashed by pre-save middleware)
    user.password = newPassword;
    await user.save();

    // Clear any stored OTPs for this phone number
    // if (redis) {
    //   try {
    //     const otpKey = `otp:${sanitizedPhone}`;
    //     const forgotOtpKey = `otp:forgot_password:${sanitizedPhone}`;
    //     await redis.del(otpKey);
    //     await redis.del(forgotOtpKey);
    //   } catch (redisError) {
    //     console.warn('Redis OTP cleanup failed:', redisError.message);
    //   }
    // }

    // console.log('✅ Password reset successful for:', sanitizedPhone);

    return res.status(200).json({
      success: true,
      message:
        "Password reset successfully. You can now login with your new password.",
    });
  } catch (error) {
    console.error("❌ Reset password error:", error);
    return res.status(500).json({
      success: false,
      message: "Server error while resetting password",
      ...(process.env.NODE_ENV === "development" && { error: error.message }),
    });
  }
};

// Test Intrekt WhatsApp Integration
export const testIntrektIntegration = async (req, res) => {
  try {
    const { phoneNumber, testType = "connection" } = req.body;

    console.log("🧪 Testing Intrekt Integration...");
    console.log("📱 Phone Number:", phoneNumber);
    console.log("🔧 Test Type:", testType);

    if (testType === "connection") {
      // Test API connection
      const connectionResult = await intrektService.testConnection();

      return res.json({
        success: true,
        message: "Intrekt connection test completed",
        data: {
          testType: "connection",
          result: connectionResult,
        },
      });
    } else if (testType === "otp" && phoneNumber) {
      // Test OTP sending
      const sanitizedPhone = sanitize(phoneNumber);
      const testOTP = "123456"; // Fixed test OTP

      const otpResult = await intrektService.sendOTP(sanitizedPhone, testOTP);

      return res.json({
        success: true,
        message: "Intrekt OTP test completed",
        data: {
          testType: "otp",
          phoneNumber: sanitizedPhone,
          testOTP: testOTP,
          result: otpResult,
        },
      });
    } else if (testType === "message" && phoneNumber) {
      // Test custom message sending
      const sanitizedPhone = sanitize(phoneNumber);

      const messageResult = await intrektService.sendMessage(
        sanitizedPhone,
        "test_template", // Replace with your actual template name
        ["Test Message from FASTAGCAB"], // Body values
        [], // Header values
        {} // Button values
      );

      return res.json({
        success: true,
        message: "Intrekt message test completed",
        data: {
          testType: "message",
          phoneNumber: sanitizedPhone,
          result: messageResult,
        },
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "Invalid test type or missing phone number",
        availableTests: ["connection", "otp", "message"],
      });
    }
  } catch (error) {
    console.error("🚨 Intrekt Integration Test Error:", error);
    res.status(500).json({
      success: false,
      message: "Error testing Intrekt integration",
      error: error.message,
    });
  }
};
