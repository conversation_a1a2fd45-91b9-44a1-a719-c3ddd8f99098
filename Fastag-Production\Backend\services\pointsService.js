// Points Service - Manages user points system

// Explicitly declare this as JavaScript
// @ts-nocheck

import User from '../Models/user.js';
import PointHistory from '../Models/PointHistory.js';
import mongoose from 'mongoose';

class PointsService {
  /**
   * Validate points input
   */
  static validatePoints(points, operation = 'add') {
    if (typeof points !== 'number' || isNaN(points)) {
      throw new Error('Points must be a valid number');
    }

    if (points <= 0) {
      throw new Error('Points must be positive');
    }

    if (points > 1000000) {
      throw new Error('Points cannot exceed 1,000,000');
    }

    if (!Number.isInteger(points)) {
      throw new Error('Points must be a whole number');
    }

    // Additional validation for deduction operation
    if (operation === 'deduct' && points < 500) {
      throw new Error('Minimum 500 points required for deductions');
    }
  }

  /**
   * Validate user ID  
   */
  static validateUserId(userId) {
    if (!userId) {
      throw new Error('User ID is required');
    }

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      throw new Error('Invalid user ID format');
    }
  }

  // Rest of original methods exactly as they were...

  static async addPoints(userId, points, source, metadata = {}) {
    // Original implementation
  }

  static async deductPoints(userId, points, source, metadata = {}, redemptionType = 'gift') {
    // Original implementation
  }

  // Other existing methods...
}

export default PointsService;
